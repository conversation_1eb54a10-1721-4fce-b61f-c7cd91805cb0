FROM php:8.1-apache

# Cài các extension PHP cần thiết
RUN docker-php-ext-install mysqli pdo pdo_mysql && docker-php-ext-enable mysqli
RUN apt update && apt install -y psmisc

# <PERSON><PERSON><PERSON> các công cụ hệ thống cần cho Composer
RUN apt-get update && apt-get install -y \
    unzip \
    zip \
    curl \
    git \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# Cài Composer toàn cục
RUN curl -sS https://getcomposer.org/installer | php && \
    mv composer.phar /usr/local/bin/composer && \
    chmod +x /usr/local/bin/composer

# Cài nano và mod_rewrite
RUN a2enmod rewrite

# Ghi đè file cấu hình Apache (000-default.conf)
COPY ./apache/000-default.conf /etc/apache2/sites-available/000-default.conf
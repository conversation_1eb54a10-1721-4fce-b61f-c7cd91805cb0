SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

DROP DATABASE IF EXISTS `chatapp`;
CREATE DATABASE `chatapp` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
USE `chatapp`;

CREATE TABLE `accounts` (
  `userID` varchar(17) NOT NULL,
  `email` varchar(50) NOT NULL UNIQUE,
  `username` varchar(32) NOT NULL,
  `password` varchar(255) NOT NULL,
  `avatar` varchar(255) DEFAULT NULL,
  `active` tinyint(1) DEFAULT 0,
  `login_token` varchar(255) UNIQUE,
  PRIMARY KEY (`userID`)
);

CREATE TABLE `messages` (
  `msgID` int(11) NOT NULL AUTO_INCREMENT,
  `sending_user_ID` varchar(17),
  `recieving_user_ID` varchar(17),
  `message` text NOT NULL,
  `sending_status` varchar(10) DEFAULT "sent",
  PRIMARY KEY (`msgID`)
);

INSERT INTO `accounts` (`userID`, `email`, `username`, `password`, `avatar`, `active`) VALUES
("*****************", "<EMAIL>", "an", "$2y$10$ve4EFuIiVtioHk6nonSdeOKemqmqXf9BRB/ZJZSTVjrZalrmr9eQm", "defaultImg.jpg", 0),
("*****************", "<EMAIL>", "binh", "$2y$10$ve4EFuIiVtioHk6nonSdeOKemqmqXf9BRB/ZJZSTVjrZalrmr9eQm", "defaultImg.jpg", 0),
("****************1", "<EMAIL>", "hoang", "$2y$10$ve4EFuIiVtioHk6nonSdeOKemqmqXf9BRB/ZJZSTVjrZalrmr9eQm", "defaultImg.jpg", 0);

INSERT INTO `messages` (`msgID`, `sending_user_ID`, `recieving_user_ID`, `message`, `sending_status`) VALUES
(1, "*****************", "*****************", "Hey, how are you doing?", "sent"),
(2, "*****************", "*****************", "I\'m doing great, thanks!", "sent"),
(3, "****************1", "*****************", "Hello, available to chat?", "sent"),
(4, "*****************", "****************1", "Sure, what\'s up?", "sent"),
(5, "*****************", "****************1", "Meeting at 3 PM, right?", "seen"),
(6, "****************1", "*****************", "Yes, don\'t be late!", "seen"),
(7, "*****************", "*****************", "Did you check the document?", "sent"),
(8, "*****************", "*****************", "Not yet, will do soon.", "sent"),
(9, "****************1", "*****************", "I emailed you the file.", "sent"),
(10, "*****************", "****************1", "Got it, thanks!", "sent"),
(11, "****************1", "*****************", "Remember", "seen");

ALTER TABLE `messages`
  ADD FOREIGN KEY (`sending_user_ID`) REFERENCES `accounts` (`userID`) ON DELETE SET NULL,
  ADD FOREIGN KEY (`recieving_user_ID`) REFERENCES `accounts` (`userID`) ON DELETE SET NULL;
COMMIT;

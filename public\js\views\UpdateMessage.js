export const Update = {
    changeActiveStatus
    // loadNewMessageFromAno,
    // updateSendingStatus
}

// Auto update the active status of the user
function changeActiveStatus(activeStatus) {
    if (activeStatus) {
        $("#active-status").children("i").attr("class", "fa-solid fa-circle me-1 text-success")
        $("#active-status").children("span").attr("class", "text-success")
        $("#active-status").children("span").html("Online")
    } else {
        $("#active-status").children("i").attr("class", "fa-solid fa-circle me-1 text-secondary")
        $("#active-status").children("span").attr("class", "text-secondary")
        $("#active-status").children("span").html("Offline")
    }
}

// Auto update message from another
// async function loadNewMessageFromAno() {
//     let res = await fetch("ManageDB/Message.php?function=loadNewMessageFromAno")
//     let newMessage = await res.json()

//     if (newMessage.length !== 0) {
//         message = newMessage[0]
//         showNewMessage(message["message"], "another")
//     }
// }

// Update your sending status when the another join the chat
// async function updateSendingStatus() {
//     let res = await fetch("ManageDB/Message.php?function=isSeen")
//     let newMessage = await res.json()

//     if ((newMessage.length === 2 && newMessage[0]["msgID"] > newMessage[1]["msgID"]) || newMessage.length === 1) {
//         $("#sending-status") ? $("#sending-status").remove() : ""
//         showSendingStatus(newMessage[0])
//     }
// }
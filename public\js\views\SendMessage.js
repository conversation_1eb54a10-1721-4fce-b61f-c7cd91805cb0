import { Info } from "./Information.js"
import { Load } from "./LoadMessage.js"

export const Send = {
    sendMessage,
    showNewMessage
}

let ws = Info.ws;

// Sending new message from YOU
async function sendMessage() {
    $("#new-chat").remove()

    if ($("#new-message").val().trim() === "") {
        return;
    }

    let newMessage = new FormData()
    newMessage.append("newMessage", $("#new-message").val())

    $("#sending-status").remove()
    showNewMessage($("#new-message").val(), "you")
    $("#message-box").append(`<div id="sending-status" class="ms-auto me-2">Delivering</div>`)
    $("#new-message").val("")

    let res = await fetch("/chatbox?method=sendMessage", {
        method: "post",
        body: newMessage
    })

    let result = await res.json();
    if (result["success"] === false) {
        alert(result["message"])
        return
    }

    let message = result["data"]
    $("#sending-status").remove()
    Load.showSendingStatus(message)

    ws.send(JSON.stringify({
        to: message["recieving_user_ID"],
        message: message["message"]
    }))

    scrollDown()
}

ws.onmessage = async (event) => {
    let message = JSON.parse(event.data)
    showNewMessage(message["message"], "another")
}

function showNewMessage(newMessage, sender) {
    $("#new-chat").remove()

    // Check the sending person and the previous message
    let lastMessage = $("#message-box").children().last()

    // Create 2 types of message
    let you = $(`
        <div class="you d-flex justify-content-end">
            <span></span>
        </div>
    `)
    let chatmate = $(`
        <div class="chatmate d-flex align-items-end">
            <div class="me-1" style="width: 30px;">
                <img src="${Info.chatmateAvatarSrc}" class="rounded-circle object-fit-cover" width="100%" height="30" alt="Avatar">
            </div>
            <span></span>
        </div>
    `)
    you.find("span").text(newMessage)
    chatmate.find("span").text(newMessage)

    // If the previous message is you and you are the sender
    if (lastMessage.hasClass("you") && sender === "you") {
        lastMessage.children("span").css("border-bottom-right-radius", "8px")
        you.children("span").css("border-radius", "20px 8px 20px 20px")
        $("#message-box").append(you)

    // If the previous message is your chatmate and your chatmate are the sender
    } else if (lastMessage.hasClass("chatmate") && sender === "chatmate") {
        lastMessage.children("span").css("border-bottom-left-radius", "8px")
        lastMessage.children("div").children("img").remove()
        chatmate.children("span").css("border-radius", "8px 20px 20px 20px")
        $("#message-box").append(chatmate)

    // If the previous message is your chatmate and he/she is the sender
    } else {
        $("#sending-status").remove()
        if (sender === "you") {
            you.css("margin-top", "5px")
            you.children("span").css("border-radius", "20px")
            $("#message-box").append(you)
        } else {
            chatmate.css("margin-top", "5px")
            chatmate.children("span").css("border-radius", "20px")
            $("#message-box").append(chatmate)
        }
    }

    scrollDown()
}

function scrollDown() {
    let box = $("#message-box")
    let scrollTop = box.scrollTop()
    let scrollHeight = box[0].scrollHeight
    let clientHeight = box.innerHeight()

    // <= 80px -> scroll
    if (scrollTop + clientHeight >= scrollHeight - 200) {
        box.scrollTop(scrollHeight)
    }
}
import { Chatbox } from "./UpdateInfo.js"

let userID = await getUserID()
let chatmateID = getChatmateID()

export const Info = {
    userID: userID,
    chatmateID: chatmateID,
    loadChatmateInfo
}

async function getUserID() {
    let res = await fetch(`/chatbox?method=getUserID`)
    let result = await res.json()
    return result["userID"]
}

function getChatmateID() {
    let urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('chatmateID');
}

async function loadChatmateInfo() {
    let res = await fetch(`/chatbox?method=getChatmateInfo`)
    let result = await res.json()
    if (result["success"] === false) {
        alert(result["message"])
        return
    }

    let chatmate = result["data"]
    $("#chatmate-avatar").eq(0).attr("src", "/images/avatars/" + chatmate["avatar"])
    $("#chatmate-name").html(chatmate["username"])
    if (chatmate["active"] === 1) {
        Chatbox.changeActiveStatus("online")
    } else {
        Chatbox.changeActiveStatus("offline")
    }

    Info.chatmateAvatarSrc = "/images/avatars/" + chatmate["avatar"]
}
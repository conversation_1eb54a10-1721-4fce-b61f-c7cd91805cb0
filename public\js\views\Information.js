import { Update } from "./UpdateMessage.js"

let userID = await getUserID()
let chatmateID = getChatmateID()

export const Info = {
    userID: userID,
    chatmateID: chatmateID,
    ws: new WebSocket(`ws://localhost:8081?userID=${userID}&chatmateID=${chatmateID}`),
    loadChatmateInfo
}

async function getUserID() {
    let res = await fetch(`/chatbox?method=getUserID`)
    let result = await res.json()
    return result["userID"]
}

function getChatmateID() {
    let urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('chatmateID');
}

async function loadChatmateInfo() {
    let res = await fetch(`/chatbox?method=getChatmateInfo`)
    let result = await res.json()
    if (result["success"] === false) {
        alert(result["message"])
        return
    }
    let chatmate = result["data"]
    $("#chatmate-avatar").eq(0).attr("src", "/images/avatars/" + chatmate["avatar"])
    $("#chatmate-name").html(chatmate["username"])
    if (chatmate["active"] === 1) {
        Update.changeActiveStatus(true)
    } else {
        Update.changeActiveStatus(false)
    }

    Info.chatmateAvatarSrc = "/images/avatars/" + chatmate["avatar"]
}
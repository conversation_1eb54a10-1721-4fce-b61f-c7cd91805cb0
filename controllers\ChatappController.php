<?php
require_once ROOT.'/models/ChatappModel.php';

Class ChatAppController {
    private $chatappModel;

    public function __construct() {
        $this->chatappModel = new ChatappModel();
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
    }

    public function index() {
        require_once ROOT."/views/chatapp.php";
    }

    public function getUserID() {
        $userID = $this->chatappModel->getUserID();
        echo json_encode(["userID" => $userID]);
    }

    public function getOwnerInfo() {
        $userID = $this->chatappModel->getUserID();
        $user = $this->chatappModel->getUserByID($userID);
        echo json_encode($user);
    }

    public function getChatmates() {
        $users = $this->chatappModel->getUsersByUsername($_SESSION['userID'], $_GET['shortUsername']);
        if ($users['success'] === false) {
            echo json_encode($users);
            return;
        } else {
            $usersData = $users['data'];
        }

        for ($i = 0; $i < count($usersData); $i++) {
            $user = $this->chatappModel->getNewestMessage($_SESSION['userID'], $usersData[$i]['userID']);
            if ($user['success'] === true) {
                $usersData[$i]['message'] = $user['data']['message'];
                $usersData[$i]['sending_user_ID'] = $user['data']['sending_user_ID'];
                $usersData[$i]['recieving_user_ID'] = $user['data']['recieving_user_ID'];
                $usersData[$i]['sending_status'] = $user['data']['sending_status'];
            } else {
                echo json_encode($user);
                return;
            }
        }
        
        $users['data'] = $usersData;
        echo json_encode($users);
    }
}
?>
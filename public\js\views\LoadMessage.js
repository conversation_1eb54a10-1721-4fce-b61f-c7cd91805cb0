import { Info } from "./Information.js"

export const Load = {
    showOldMessage,
    showSendingStatus
}

function showOldMessage(messages) {
    messages.forEach(message => {

        // Check the sending person and the previous message
        let sender = message["sending_user_ID"] === Info.userID ? "you" : "chatmate"
        let lastMessage = $("#message-box").children().first()

        // Create 2 types of message
        let you = $(`
            <div class="you d-flex justify-content-end">
                <span></span>
            </div>
        `)
        let chatmate = $(`
            <div class="chatmate d-flex align-items-end">
                <div class="me-1" style="width: 30px;"></div>
                <span></span>
            </div>
        `)
        you.find("span").text(message["message"])
        chatmate.find("span").text(message["message"])

        // If the previous message is you and you are the sender
        if (lastMessage.hasClass("you") && sender === "you") {
            lastMessage.children("span").css("border-top-right-radius", "8px")
            you.children("span").css("border-radius", "20px 20px 8px 20px")
            $("#message-box").prepend(you)

        // If the previous message is your chatmate and he/she is the sender
        } else if (lastMessage.hasClass("chatmate") && sender === "chatmate") {
            lastMessage.children("span").css("border-top-left-radius", "8px")
            chatmate.children("span").css("border-radius", "20px 20px 20px 8px")
            $("#message-box").prepend(chatmate)

        // Remaining case = starting a new chat
        } else {
            if (sender === "you") {
                you.css("margin-bottom", "5px")
                you.children("span").css("border-radius", "20px")
                $("#message-box").prepend(you)
            } else {
                chatmate.css("margin-bottom", "5px")
                chatmate.children("span").css("border-radius", "20px")
                chatmate.children().first().html(`<img src="${Info.chatmateAvatarSrc}" class="rounded-circle object-fit-cover" width="100%" height="30" alt="Avatar">`)
                $("#message-box").prepend(chatmate)
            }
        }
    });
}

// Show the message sending_status
function showSendingStatus(message) {
    if (message["sending_user_ID"] === Info.userID) {
        if (message["sending_status"] === "sent") {
            $("#message-box").append(`<div id="sending-status" class="ms-auto me-2">Sent</div>`)
        } else {
            $("#message-box").append(`
                <span id="sending-status" class="ms-auto me-2" style="width: 20px;">
                    <img src="${Info.chatmateAvatarSrc}" class="rounded-circle object-fit-cover" width="100%" height="20" alt="Avatar">
                </span>
            `)
        }
    }
}
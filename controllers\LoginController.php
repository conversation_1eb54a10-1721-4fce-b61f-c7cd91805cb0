<?php
require_once ROOT.'/models/LoginModel.php';

Class LoginController {
    private $loginModel;

    public function __construct() {
        $this->loginModel = new LoginModel();
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
    }

    public function index() {
        if (isset($_COOKIE['login_token'])) {
            $result = $this->loginModel->reLogin($_COOKIE['login_token']);
            if ($result) {
                header("Location: /chatapp");
                exit();
            }
        }
        require_once ROOT."/views/login.php";
    }

    public function login() {
        $email = $_POST['email'];
        $password = $_POST['pass'];
        $result = $this->loginModel->login($email, $password);
        echo json_encode($result);
    }

    public function register() {
        $email = $_POST['email'];
        $username = $_POST['username'];
        $password = $_POST['pass'];
        $avatar = $_FILES['avatar'];
        $result = $this->loginModel->register($email, $username, $password, $avatar);
        echo json_encode($result);
    }
}
?>
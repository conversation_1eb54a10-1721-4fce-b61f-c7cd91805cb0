let toggle = true // true = select search; false == searching
let search = $("form").html()
let ws
export { ws }

$(document).ready(async () => {
    history.pushState(null, '', '/chatapp')

    if (ws === undefined) {
        let res = await fetch(`/chatapp?method=getUserID`)
        let result = await res.json()
        let userID = result["userID"]
        ws = new WebSocket(`ws://localhost:8081?userID=${userID}`)
        console.log(ws)
    }

    let res = await fetch('/chatapp?method=getOwnerInfo')
    let user = await res.json()
    if (user["success"] === true) {
        user = user["data"]
        $("#user-avatar").attr("src", "/images/avatars/" + user["avatar"])
        $("#username").html(user["username"])
    } else {
        $('#chat-box').html(`
            <div class="d-flex align-items-center border-bottom pb-3">
                <div class="fs-5 text-danger fw-bold">Loading user profile failed</div>
                <a class="btn btn-danger align-self-center ms-auto fs-4" href="/logout" style="height: 50px;">Logout</a>
            </div>
        `)
    }
})

$(document).on("click", "#search", () => {

    // select search -> searching
    if (toggle === true) {
        $("form > div").replaceWith($(`
            <div class="input-group">
                <input id="search-box" class="form-control" type="text" name="username" placeholder="Enter name to search..." autocomplete="off">
                <span id="search" class="input-group-text" style="cursor: pointer;"><i class="fa-solid fa-x"></i></span>
            </div>    
        `))
        $("#search-box").focus()
        toggle = false

    // searching -> select search
    } else {
        $("form > div").replaceWith(search)
        $(".list-group").empty()
        toggle = true
    }
})

// Show peole while searching
$(document).on("input", "#search-box", async function() {
    let shortUsername = $("#search-box").val().trim() // A part of an other username
    let res = await fetch(`/chatapp?method=getChatmates&shortUsername=${shortUsername}`)
    
    if (res.ok) {
        let users = await res.json()
        if (users["success"] === false) {
            alert(users["message"])
            return
        } else {
            users = users["data"]
        }

        $(".list-group").empty()
        users.forEach(user => {
            let UI = listUI(user["active"], user["message"], user["sending_user_ID"], 
                user["recieving_user_ID"], user["userID"], user["sending_status"])

            $(".list-group").append(`
                <li class="list-group-item rounded d-flex align-items-center" onclick="moveChatBox('${user["userID"]}')">
                    <img class="rounded-circle object-fit-cover me-3" src="/images/avatars/${user["avatar"]}" width="50" height="50">
                    <div class="d-flex flex-column">
                        <div class="fw-bold fs-5">${user["username"]}</div>
                        <div class="new-message ${UI["bold"]}">${UI["message"]}</div>
                    </div>
                    <div class="ms-auto"><i class="fa-solid fa-circle ${UI["iconColor"]} me-2"></i></div>
                </li>
            `)
        });
    }
})

ws.onmessage = async (event) => {
    let message = JSON.parse(event.data)
    if (message["code"] === "avtive status") {
        Update.changeActiveStatus(message["status"])
    } else if (message["code"] === "new message") {
        // Update the message list
    }
}

function listUI(chatmateActive, message, sender, reciever, chatmateID, sendingStatus) {
    let iconColor = chatmateActive == true ? "text-success" : ""

    // If the message too long shorten it and add "..."
    if (message.length > 30) {
        message = message.substr(0, 30) + "..."
    }

    // If the message come from another user and the status = "sent" => bold the message
    let bold
    if (!sender || (sender === chatmateID && sendingStatus === "sent")) {
        bold = "fw-bold"

    // If the message is revcieved by another user => this person is the sender
    } else if (reciever === chatmateID) {
        message = "You: " + message
        bold = ""

    // The message come from another user and already "seen"
    } else {
        bold = ""
    }

    return {"iconColor": iconColor, "message": message, "bold": bold}
}

async function moveChatBox(chatmateID) {
    window.location.href = `/chatbox?chatmateID=${chatmateID}`
}
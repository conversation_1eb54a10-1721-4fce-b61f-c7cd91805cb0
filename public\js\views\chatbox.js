import { Info } from "./Information.js"
import { Load } from "./LoadMessage.js"
import { Send } from "./SendMessage.js"

$(document).ready(async () => {

    ws.send(JSON.stringify({
        action: "setChatmate",
        chatmateID: Info.chatmateID
    }))

    // Set the chatmateID to start the chat
    await fetch(`/chatbox?method=setChatmateID&chatmateID=${Info.chatmateID}`)

    // Load chatmate infomation
    await Info.loadChatmateInfo()

    // Load message list
    let res = await fetch(`/chatbox?method=loadMessageList`)
    let result = await res.json()
    
    if (result["success"] === false) {
        alert(result["message"])
        return
    }
    let messageList = result["data"]
    
    // If there is no message
    if (messageList.length === 0) {
        $("#message-box").append(`<div id="new-chat" class="mx-auto mb-5">Type anything to start a new chat</div>`)
    } else {

        // Load message to the screen
        Load.showOldMessage(messageList)
        $("#message-box").children("div").last().removeAttr("style")
        Load.showSendingStatus(messageList[0])
        $("#message-box").scrollTop($("#message-box")[0].scrollHeight)
    }
})

$(".fa-arrow-left").on("click", async () => {
    await fetch(`/chatbox?method=removeChatmateID`)
    window.location.href = "/chatapp"
})

$("#message-box").on("scroll", async function() {
    let box = $(this)
    let scrollTop = box.scrollTop()
    let scrollHeight = box[0].scrollHeight
    let clientHeight = box[0].clientHeight
    let scrollPercent = scrollTop / (scrollHeight - clientHeight)

    if (scrollPercent < 0.5) {
        let res = await fetch(`ManageDB/Message.php?function=loadMore`)
        let messageList = (await res.json())["messageList"]
        if (messageList.length !== 0) {
            messageList.forEach(message => {
                Load.showOldMessage(message)
            });
            $("#message-box").children("div").last().removeAttr("style")
        }
    }
})

$("#new-message").on("keydown", e => {
    if (e.key === "Enter") Send.sendMessage()
})
import { initWS } from "./Websocket.js"
import { Info } from "./Information.js"
import { Load } from "./LoadMessage.js"
import { Send } from "./SendMessage.js"
import { Chatbox } from "./UpdateInfo.js"

let ws = await initWS(Info.userID, Info.chatmateID)

$(document).ready(async () => {
    setupWebSocketHandlers()

    // Set the chatmateID to start the chat
    await fetch(`/chatbox?method=setChatmateID&chatmateID=${Info.chatmateID}`)

    // Load chatmate infomation
    await Info.loadChatmateInfo()

    // Load message list
    let res = await fetch(`/chatbox?method=loadMessageList`)
    let result = await res.json()
    
    if (result["success"] === false) {
        alert(result["message"])
        return
    }
    
    let messageList = result["data"]
    
    // If there is no message
    if (messageList.length === 0) {
        $("#message-box").append(`<div id="new-chat" class="mx-auto mb-5">Type anything to start a new chat</div>`)
    } else {

        // Load message to the screen
        Load.showOldMessage(messageList)
        $("#message-box").children("div").last().removeAttr("style")
        Load.showSendingStatus(messageList[0])
        $("#message-box").scrollTop($("#message-box")[0].scrollHeight)

        // If the last message is from your chatmate
        if ($("#message-box").children().last().hasClass("chatmate")) {
            ws.send(JSON.stringify({
                code: "seen"
            }))
        }
    }
})

$(".fa-arrow-left").on("click", async () => {
    await fetch(`/chatbox?method=removeChatmateID`)
    window.location.href = "/chatapp"
})

// $("#message-box").on("scroll", async function() {
//     let box = $(this)
//     let scrollTop = box.scrollTop()
//     let scrollHeight = box[0].scrollHeight
//     let clientHeight = box[0].clientHeight
//     let scrollPercent = scrollTop / (scrollHeight - clientHeight)

//     if (scrollPercent < 0.5) {
//         let res = await fetch(`ManageDB/Message.php?function=loadMore`)
//         let messageList = (await res.json())["messageList"]
//         if (messageList.length !== 0) {
//             messageList.forEach(message => {
//                 Load.showOldMessage(message)
//             });
//             $("#message-box").children("div").last().removeAttr("style")
//         }
//     }
// })

$("#new-message").on("keydown", e => {
    if (e.key === "Enter") Send.sendMessage()
})

function setupWebSocketHandlers() {
    ws.onmessage = async (event) => {
        let data = JSON.parse(event.data)
        if (data["code"] === "new message") {
            Send.showNewMessage(data["message"], "chatmate")
            ws.send(JSON.stringify({
                code: "seen"
            }))
        } else if (data["code"] === "active status") {
            Chatbox.changeActiveStatus(data["status"])
        } else if (data["code"] === "seen") {
            Load.showSendingStatus({sending_user_ID: Info.userID, recieving_user_ID: data["from"], sending_status: "seen"})
        }
    }
    
    ws.onerror = (error) => {
        alert("WebSocket error:", error)
    }
}
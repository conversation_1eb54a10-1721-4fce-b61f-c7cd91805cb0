<?php
require_once("DB.php");
require_once('Model.php');

Class ChatboxModel extends Model {
    private $con;

    public function __construct() {
        parent::__construct();
        $this->con = DB::connect();
    }

    public function loadMessageList($userID, $chatmateID) {
        $sql = "SELECT * FROM `messages` WHERE (`sending_user_ID` = ? AND `recieving_user_ID` = ?) OR (`sending_user_ID` = ? AND `recieving_user_ID` = ?) ORDER BY `msgID` DESC LIMIT 10";
        $stmt = $this->con->prepare($sql);
        $stmt->bind_param("ssss", $userID, $chatmateID, $chatmateID, $userID);
        if ($stmt->execute()) {
            $result = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
            return ["success" => true, "data" => $result];
        } else {
            return ["success" => false, "message" => "Error executing query"];
        }
    }

    public function addMessage($userID, $chatmateID, $message) {
        $sql = "INSERT INTO `messages` (`sending_user_ID`, `recieving_user_ID`, `message`) VALUES (?, ?, ?)";
        $stmt = $this->con->prepare($sql);
        $stmt->bind_param("sss", $userID, $chatmateID, $message);
        if ($stmt->execute()) {
            $messageID = $stmt->insert_id;
            $sql = 'SELECT * FROM `messages` WHERE `msgID` = ?';
            $stmt = $this->con->prepare($sql);
            $stmt->bind_param("i", $messageID);
            $stmt->execute();
            $result = $stmt->get_result()->fetch_assoc();
            
            return ["success" => true, "data"=> $result];
        } else {
            return ["success"=> false,"message"=> "Error executing query"];
        }
    }
}
?>
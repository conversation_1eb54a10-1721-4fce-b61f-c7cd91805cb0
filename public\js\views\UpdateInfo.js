// Update the active status (online/offline) AND new message

export const Chatapp = {
    changeActiveStatus : ca_changeActiveStatus,
    newMessage : ca_newMessage
}

export const Chatbox = {
    changeActiveStatus : cb_changeActiveStatus
}


/*================ Chatapp ================*/

// Update the active status of the user
function ca_changeActiveStatus(activeStatus, userID) {
    let color = activeStatus === "online" ? "text-success" : "text-dark"
    let $icon = $(`li[data-chatmate-id="${userID}"] i`)
    $icon.removeClass("text-success text-dark")
    $icon.addClass(color)
}

// Update the message if there is a new message
async function ca_newMessage(newMessage, chatmateID) {
    let replacedUser = $(`li[data-chatmate-id=${chatmateID}]`)
    replacedUser.find(".new-message").html(newMessage)
    replacedUser.find(".new-message").removeAttr("class").addClass(`new-message fw-bold`)
}




/*================ Chatbox ================*/

// Update the active status of the user
function cb_changeActiveStatus(activeStatus) {
    $("#active-status").children("i").removeClass("text-success text-dark")

    if (activeStatus === "online") {
        $("#active-status").children("i").addClass("text-success")
        $("#active-status").children("span").attr("class", "text-success")
        $("#active-status").children("span").html("Online")
    } else {
        $("#active-status").children("i").addClass("text-dark")
        $("#active-status").children("span").attr("class", "text-dark")
        $("#active-status").children("span").html("Offline")
    }
}
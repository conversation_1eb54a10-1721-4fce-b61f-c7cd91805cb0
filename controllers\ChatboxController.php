<?php
require_once ROOT.'/models/ChatboxModel.php';

Class ChatBoxController {
    private $chatboxModel;

    public function __construct() {
        $this->chatboxModel = new ChatBoxModel();
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
    }

    public function index() {
        require_once ROOT."/views/chatbox.php";
    }

    public function setChatmateID() {
        $_SESSION["chatmateID"] = $_GET["chatmateID"];
    }

    public function removeChatmateID() {
        unset($_SESSION["chatmateID"]);
    }

    public function getUserID() {
        $userID = $this->chatboxModel->getUserID();
        echo json_encode(["userID" => $userID]);
    }

    public function getChatmateInfo() {
        $chatmate = $this->chatboxModel->getUserByID($_SESSION["chatmateID"]);
        echo json_encode($chatmate);
    }

    public function loadMessageList() {
        $result = $this->chatboxModel->loadMessageList($_SESSION['userID'], $_SESSION['chatmateID']);
        echo json_encode($result);
    }

    public function sendMessage() {
        $newMessage = $_POST['newMessage'];
        $result = $this->chatboxModel->addMessage($_SESSION['userID'], $_SESSION['chatmateID'], $newMessage);
        echo json_encode($result);
    }
}
?>
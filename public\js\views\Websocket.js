let port = 8081
let ws = null

export async function initWS(userID, chatmateID = null) {
    if (ws && (ws.readyState === WebSocket.OPEN || ws.readyState === WebSocket.CONNECTING)) {
        return ws
    }

    if (chatmateID === null) {
        ws = new WebSocket(`ws://localhost:${port}?userID=${userID}`)
    } else {
        ws = new WebSocket(`ws://localhost:${port}?userID=${userID}&chatmateID=${chatmateID}`)
    }
    return ws
}
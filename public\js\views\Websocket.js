let port = 8081
let ws = null

export async function initWS(userID, chatmateID = null) {
    // if ws already exist and is open, return it
    if (ws && ws.readyState === WebSocket.OPEN) {
        return ws
    }

    // if ws already exist and is connecting or closing, close it
    if (ws && ws.readyState !== WebSocket.CLOSED) {
        ws.close()
    }

    // create new ws
    if (chatmateID === null) {
        ws = new WebSocket(`ws://localhost:${port}?userID=${userID}`)
    } else {
        ws = new WebSocket(`ws://localhost:${port}?userID=${userID}&chatmateID=${chatmateID}`)
    }

    // wait for ws to be open
    return new Promise((resolve, reject) => {
        ws.onopen = () => {
            resolve(ws)
        }

        ws.onerror = (error) => {
            reject(error)
        }

        // timeout after 10 seconds
        setTimeout(() => {
            if (ws.readyState !== WebSocket.OPEN) {
                ws.close()
                reject(new Error('WebSocket connection timeout'))
            }
        }, 10000)
    })
}
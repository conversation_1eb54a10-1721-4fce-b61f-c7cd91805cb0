<?php
require_once("DB.php");

Class Model {
    private $con;

    public function __construct() {
        $this->con = DB::connect();
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }
    }

    public function getUserID() {
        return $_SESSION["userID"];
    }

    public function getUserByID($userID) {
        $sql = "SELECT * FROM `accounts` WHERE `userID` = ?";
        $stmt = $this->con->prepare($sql);
        $stmt->bind_param("s", $userID);
        if ($stmt->execute()) {
            $row = $stmt->get_result();
            if ($row->num_rows > 0) {
                $result = $row->fetch_assoc();
                return ["success" => true, "data" => $result];
            } else {
                return ["success" => false, "message" => "User not found"];
            }
        } else {
            return ["success" => false, "message" => "Error executing query"];
        }
    }
}
?>
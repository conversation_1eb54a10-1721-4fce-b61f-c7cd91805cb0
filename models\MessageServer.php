<?php
use Ratchet\MessageComponentInterface;
use Ratchet\ConnectionInterface;
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/DB.php';

class Chat implements MessageComponentInterface {
    public $clients;
    private $dbcon;

    public function __construct() {
        $this->clients = new \SplObjectStorage;
        $this->dbcon = DB::connect();
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
    }

    public function onOpen(ConnectionInterface $conn) {

        // Store the new connection to send messages to later
        parse_str($conn->httpRequest->getUri()->getQuery(), $query);
        $userID = $query['userID'] ?? null;
        $chatmateID = $query['chatmateID'] ?? null;
        $this->clients->attach($conn, ['userID' => $userID, 'chatmateID' => $chatmateID]);
        
        $this->setActive($conn, 'online');

        echo 'New connection! (userID: ' . $userID . ')' . ' (chatmateID: ' . $chatmateID . ')' . "\n";
    }

    public function onMessage(ConnectionInterface $from, $msg) {
        $data = json_decode($msg, true);

        if ($data["action"] === "setChatmate") {
            $chatmateID = $data["chatmateID"];
            $this->updateClientInfo($from, ['chatmateID' => $chatmateID]);
        } else {
            $to = $data['to'] ?? null;
            $message = $data['message'];

            foreach ($this->clients as $client) {
                $chatmateID = $this->clients[$client]["chatmateID"] ?? null;
                if ($to === $chatmateID) {

                    // The sender is not the receiver, send to each client connected
                    $client->send(json_encode(['code' => 'new message', 'from' => $this->clients[$from]['chatmateID'], 'message' => $message]));
                    break;
                }
            }
        }
    }

    public function onClose(ConnectionInterface $conn) {

        // The connection is closed, remove it
        $this->setActive($conn, 'offline');
        $this->clients->detach($conn);

        echo "Connection $conn->resourceId closed\n";
    }

    public function onError(ConnectionInterface $conn, \Exception $e) {
        echo "An error has occurred: {$e->getMessage()}\n";
        $conn->close();
    }

    private function updateClientInfo(ConnectionInterface $conn, array $newInfo) {
        $oldInfo = $this->clients->contains($conn) ? $this->clients[$conn] : [];

        if (!is_array($oldInfo)) {
            $oldInfo = []; // fallback nếu không phải array
        }

        $this->clients->detach($conn);
        $this->clients->attach($conn, array_merge($oldInfo, $newInfo));
    }

    private function setActive(ConnectionInterface $conn, $status) {
        $userID = $this->clients[$conn]["userID"] ?? null;

        if ($status === 'online') {
            $sql = "UPDATE `accounts` SET `active` = 1 WHERE `userID` = ?";
        } else {
            $sql = "UPDATE `accounts` SET `active` = 0 WHERE `userID` = ?";
        }
        $stmt = $this->dbcon->prepare($sql);
        $stmt->bind_param("s", $userID);
        $stmt->execute();
        $stmt->close();

        foreach ($this->clients as $client) {
            $client->send(json_encode(['code' => 'avtive status', 'status' => $status, 'userID' => $userID]));
        }
    }
}

// Khởi tạo server WebSocket
$server = \Ratchet\Server\IoServer::factory(
    new \Ratchet\Http\HttpServer(
        new \Ratchet\WebSocket\WsServer(
            new Chat()
        )
    ),
    8081
);

echo "WebSocket server is running at port: 8081\n";
$server->run();
?>

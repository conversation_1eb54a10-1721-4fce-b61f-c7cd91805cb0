<?php
use Ratchet\MessageComponentInterface;
use Ratchet\ConnectionInterface;
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/DB.php';

class Chat implements MessageComponentInterface {
    public $clients;
    private $dbcon;

    public function __construct() {
        $this->clients = new \SplObjectStorage;
        $this->dbcon = DB::connect();
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
    }

    public function onOpen(ConnectionInterface $conn) {

        // Store the new connection to send messages to later
        parse_str($conn->httpRequest->getUri()->getQuery(), $query);
        $userID = $query['userID'] ?? null;
        $chatmateID = $query['chatmateID'] ?? null;
        $this->clients->attach($conn, ['userID' => $userID, 'chatmateID' => $chatmateID]);
        
        $this->setActive($conn, 'online');

        echo 'New connection! (userID: ' . $userID . ')' . ' (chatmateID: ' . $chatmateID . ')' . "\n";
    }

    public function onMessage(ConnectionInterface $from, $msg) {
        $data = json_decode($msg, true);
        $chatmateID = $this->clients[$from]["chatmateID"];
        $to = null;

        foreach ($this->clients as $client) {
            if ($client !== $from && $this->clients[$client]['userID'] === $chatmateID) {
                $to = $client;
                break;
            }
        }

        if ($to === null) {
            return;
        }

        // The message is "seen"
        if ($data['code'] === 'seen') {
            $to->send(json_encode(['code' => 'seen', 'from' => $this->clients[$from]['userID']]));
            
        // New message is sent
        } else if ($data['code'] === 'new message') {
            if ($this->clients[$to]['chatmateID'] === $this->clients[$from]['userID']) {
                $msgID = $data['msgID'] ?? null;

                $sql = "UPDATE `messages` SET `sending_status` = 'seen' WHERE `msgID` = ?";
                $stmt = $this->dbcon->prepare($sql);
                $stmt->bind_param("i", $msgID);
                $stmt->execute();
                $stmt->close();
            }

            $message = $data['message'] ?? "";
            $to->send(json_encode(['code' => 'new message', 'from' => $this->clients[$from]['userID'], 'message' => $message]));
        }
    }

    public function onClose(ConnectionInterface $conn) {

        // The connection is closed, remove it
        $this->setActive($conn, 'offline');
        $this->clients->detach($conn);

        echo "Connection $conn->resourceId closed\n";
    }

    public function onError(ConnectionInterface $conn, \Exception $e) {
        echo "An error has occurred: {$e->getMessage()}\n";
        $conn->close();
    }

    private function setActive(ConnectionInterface $conn, $status) {
        $userID = $this->clients[$conn]["userID"] ?? null;

        if ($status === 'online') {
            $sql = "UPDATE `accounts` SET `active` = 1 WHERE `userID` = ?";
        } else {
            $sql = "UPDATE `accounts` SET `active` = 0 WHERE `userID` = ?";
        }
        $stmt = $this->dbcon->prepare($sql);
        $stmt->bind_param("s", $userID);
        $stmt->execute();
        $stmt->close();

        foreach ($this->clients as $client) {
            $client->send(json_encode(['code' => 'active status', 'status' => $status, 'userID' => $userID]));
        }
    }
}

// Khởi tạo server WebSocket
$server = \Ratchet\Server\IoServer::factory(
    new \Ratchet\Http\HttpServer(
        new \Ratchet\WebSocket\WsServer(
            new Chat()
        )
    ),
    8081
);

echo "WebSocket server is running at port: 8081\n";
$server->run();
?>

<?php
Class LogoutController {
    public function __construct() {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
    }

    public function index() {
        if (isset($_COOKIE['login_token'])) {
            setcookie('login_token', '', time() - 3600, '/');
        }
        session_unset();
        session_destroy();
        header("Location: /login");
        exit();
    }
}
?>
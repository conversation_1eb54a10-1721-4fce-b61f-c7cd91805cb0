<?php
require_once("DB.php");

Class LoginModel {
    private $con;

    public function __construct() {
        $this->con = DB::connect();
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }
    }

    public function login($email, $password) {
        $sql = "SELECT * FROM accounts WHERE email = ?";
        $stmt = $this->con->prepare($sql);
        $stmt->bind_param("s", $email);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows === 0) {
            return ["success" => false, "message" => "Email or password is incorrect"];
        } else if ($result->num_rows > 0) {
            $row = $result->fetch_assoc();
            if (password_verify($password, $row['password'])) {

                // Logout if user is already logged in
                if (isset($_SESSION['userID'])) {
                    if (isset($_COOKIE['login_token'])) {
                        setcookie('login_token', '', time() - 3600, '/');
                    }
                    session_unset();
                    session_destroy();
                }

                // Login
                $attempt = 0;
                $maxAttemps = 10;
                do {
                    $token = bin2hex(random_bytes(64));
                    $sql = 'UPDATE `accounts` SET `login_token` = ? WHERE `userID` = ?';
                    $stmt = $this->con->prepare($sql);
                    $stmt->bind_param("ss", $token, $row['userID']);
                    if ($stmt->execute()) {
                        setcookie('login_token', $token, time() + 3600, '/');
                        $_SESSION['userID'] = $row['userID'];
                        break;
                    } else if ($stmt->errno == 1062) {
                        $attempt++;
                    } else {
                        return ['success'=> false, 'message'=> $stmt->error];
                    }
                } while ($attempt < $maxAttemps);

                return ["success" => true];
            } else {
                return ["success" => false, "message" => "Email or password is incorrect"];
            }
        }
    }

    public function reLogin($token) {
        $sql = "SELECT `userID` FROM `accounts` WHERE `login_token` = ?";
        $stmt = $this->con->prepare($sql);
        $stmt->bind_param("s", $token);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($row = $result->fetch_assoc()) {
            $_SESSION['userID'] = $row['userID'];
            return ["success" => true];
        } else {
            return ["success" => false, "message" => "Invalid token"];
        }
    }

    private function generateUserID() {
        $timestamp = date('YmdHis');
        $rand = rand(100, 999);
        return $timestamp . $rand;
    }

    private function saveAvatar($avatar, $userID) {
        // Image handle
        $avatar = $_FILES["avatar"];
        $avatarDir = "../storage/avatars/";
        $avatarPath = "";
        $allowType = ["image/png", "image/gif", "image/jpeg"];
        $maxSize = 5 * 1024 * 1024;

        // Upload image file
        if (!empty($avatar["name"])) {

            if ($avatar["error"] !== 0) {
                return ["success"=> false, "message"=> 'Upload image fail. Please try again!'];
            }
            if (!in_array($avatar["type"], $allowType)) {
                return ["success"=> false, "message"=> "Invalid file type. Only PNG, GIF, JPEG, JPG allowed!"];
            }
            if ($avatar["size"] > $maxSize) {
                return ["success"=> false, "message"=> "File size exceeds 5MB!"];
            }

            $avatarExt = pathinfo($avatar["name"], PATHINFO_EXTENSION);
            $avatarName = "avatar_" . $userID . "." . $avatarExt;
            $avatarPath = $avatarDir . $avatarName;

            if (!move_uploaded_file($avatar["tmp_name"], $avatarPath)) {
                return ["success"=> false, "message"=> "Save image fail. Please try again!"];
            }

        } else {
            $avatarPath = $avatarDir . "defaultImg.jpg";
        }
        return ["success"=> true, "name"=> $avatarName];
    }

    public function register($email, $username, $password, $avatar) {
        $userID = $this->generateUserID();
        $password = password_hash($password, PASSWORD_DEFAULT);

        $saveResult = $this->saveAvatar($avatar, $userID);
        if ($saveResult["success"] === false) {
            return ["success" => false, "message" => $saveResult["message"]];
        } else {
            $avatarName = $saveResult["name"];
        }

        $sql = "SELECT * FROM accounts WHERE email = ?";
        $stmt = $this->con->prepare($sql);
        $stmt->bind_param("s", $email);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            return ["success" => false, "message" => "Email already exists"];
        } else {
            $sql = "INSERT INTO `accounts` (`userID`, `email`, `username`, `password`, `avatar`) VALUES (?, ?, ?, ?, ?)";
            $stmt = $this->con->prepare($sql);
            $stmt->bind_param("sssss", $userID, $email, $username, $password, $avatarName);
            $stmt->execute();
            return ["success" => true];
        }
    }
}
?>
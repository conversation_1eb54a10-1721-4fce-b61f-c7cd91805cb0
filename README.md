# 🚀 [Chat App Application] 🚀

## Enter the container:
docker-compose ps (to get the container name)
docker exec -it <container_name> <command> (command: bash)
- This project: **docker exec -it chat_app-web-1 bash**

## Run the project
docker-compose up --build

## Clear port 8001
docker exec -it chat_app-web-1 bash
fuser -k 8081/tcp

## Run server
docker exec -it <container_name> <command> -c "cd /var/www/models && php MessageServer.php" (command: bash)
- This project: **docker exec -it chat_app-web-1 bash -c "cd /var/www/models && php MessageServer.php"**

<?php
// Function route
function route($controller, $queries) {
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
    
    if (!isset($_COOKIE['login_token'])) {
        session_unset();
        session_destroy();
    }

    if (isset($_SESSION['userID']) && $controller === '') {
        $controller = 'chatapp';
        $queries['method'] = 'index';
    }

    if (!isset($_SESSION['userID']) && !($controller === 'login' && ($queries['method'] === 'login' || $queries['method'] === 'register'))) {
        $controller = 'login';
        $queries['method'] = 'index';
    }

    $controller = ucfirst($controller . "Controller");
    if (file_exists(ROOT."/controllers/" . $controller . ".php")) {
        require_once ROOT."/controllers/{$controller}.php";
        $controller = new $controller();
        if (method_exists($controller, $queries["method"])) {
            $method = $queries["method"];
            $controller->$method();
        } else {
            page_not_found();
        }
    } else {
        page_not_found();
    }
}

// Page not found
function page_not_found() {
    http_response_code(404);
    require_once ROOT."/views/404.php";
}
?>
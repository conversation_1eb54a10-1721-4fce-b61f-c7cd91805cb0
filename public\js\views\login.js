let tabStatus = true // true = LOGIN mode; false = REGISTER mode
let loginText = $(".form-body").html()

$(document).ready(() => {
    history.pushState(null, '', '/login')
})

// Switch to the LOGIN mode
$(document).on("click", "#login", () => {
    $("#register").removeAttr("style")
    $("form").removeAttr("enctype")
    
    if (tabStatus === false) {
        let formBody = $("<div class='form-body'>").html(loginText)
        
        $(".form-body").replaceWith(formBody)
        $("button[type='submit']").text("Login")
        $("#login").css({
            "backgroundColor": "rgb(36, 41, 102)",
            "color": "white"
        })

        tabStatus = true
    }
})

// Switch to the REGISTER mode
$(document).on("click", "#register", () => {
    $("#login").removeAttr("style")
    $("form").attr("enctype", "multipart/form-data")

    if (tabStatus === true) {
        let formBody = $("<div class='form-body'>").html(`
            <div class="form-group mb-3">
                <label for="email" class="form-label">Email<span style="color: red;">*</span></label>
                <input type="email" class="form-control" id="email" name="email" placeholder="Enter your email" required>
            </div>
            <div class="form-group mb-3">
                <label for="username" class="form-label">Username<span style="color: red;">*</span></label>
                <input type="text" class="form-control" id="username" name="username" placeholder="Enter your username" required>
            </div>
            <div class="form-group mb-3">
                <label for="pass" class="form-label">Password<span style="color: red;">*</span></label>
                <div class="input-group">
                    <input type="password" class="form-control" id="pass" name="pass" placeholder="Enter your password" required>
                    <span class="input-group-text see-pass" style="cursor: pointer;"><i class="fa-solid fa-eye"></i></span>
                </div>
                <div id="check-pass" class="fs-6 fst-italic text-danger" style="display: none;">Password must be longer than 6 characters</div>
            </div>
            <div class="form-group mb-3">
                <label for="pass-again" class="form-label">Enter password again<span style="color: red;">*</span></label>
                <div class="input-group">
                    <input type="password" class="form-control" id="pass-again" placeholder="Enter your password again" required>
                    <span class="input-group-text see-pass" style="cursor: pointer;"><i class="fa-solid fa-eye"></i></span>
                </div>
                <div id="check-pass-again" class="fs-6 fst-italic text-danger" style="display: none;">Passwords do not match</div>
            </div>
            <div class="form-group mb-3">
                <label for="formImg" class="form-label">Avatar</label>
                <input class="form-control" type="file" id="formImg" accept="image/png,image/gif,image/jpeg" name="avatar">
            </div>
        `)

        $(".form-body").replaceWith(formBody)
        $("button[type='submit']").text("Register")
        $("#register").css({
            "backgroundColor": "rgb(36, 41, 102)",
            "color": "white"
        })

        tabStatus = false
    }
})

// Show the password
$(document).on("click", ".see-pass", function() {
    let currentStatus = changeEye($(this))
    let password = $(this).parent().children("input")

    if (currentStatus === "not show") {
        password.attr("type", "text")
    } else {
        password.attr("type", "password")
    }

    // Change the eye icon
    function changeEye(icon) {
        icon = icon.children().first()
        if (icon.hasClass("fa-eye-slash")) {
            icon.replaceWith("<i class='fa-solid fa-eye'></i>")
            return "show"
        } else if (icon.hasClass("fa-eye")) {
            icon.replaceWith("<i class='fa-solid fa-eye-slash'></i>")
            return "not show"
        }
    }
})

// Remove error message when input
$(document).on("input", "input", () => {
    $("#error-message").css({"display": "none"}).html("")
})

// Check the password in REGISTER mode
$(document).on("click", "#register", () => {

    // Password must have at least 6 characters
    $("#pass").off("input").on("input", function() {
        if ($("#pass").val() !== "" && $(this).val().length < 6) {
            $("#check-pass").css({"display": "block"})
        } else {
            $("#check-pass").css({"display": "none"})
        }
        $("#pass-again").trigger("input")
    })

    // Password must match together
    $("#pass-again").off("input").on("input", function() {
        if ($("#pass-again").val() !== "" && $(this).val() !== $("#pass").val()) {
            $("#check-pass-again").css({"display": "block"})
        } else {
            $("#check-pass-again").css({"display": "none"})
        }
    })
})

// When the form submit
$(document).on("submit", "form", async function(e) {
    // Disable submit button
    e.preventDefault()

    // Form is valided with broswer check
    if (!this.checkValidity()) {
        this.reportValidity()
        return
    }

    // Password < 6
    if ($("#pass").val().length < 6) {
        $('#error-message').css({"display": "block"}).html("Password must be longer than 6 characters")
        hightlightError()

    // Password match
    } else if (tabStatus === false && $("#pass").val() !== $("#pass-again").val()) {
        $('#error-message').css({"display": "block"}).html("Passwords do not match")
        hightlightError()
    
    // Login mode
    } else if (tabStatus === true) {
        let form = new FormData(this)
        let res = await fetch("/login?method=login" , {
            method: "post",
            body: form
        })

        if (res.ok) {
            let result = await res.json()
            if (result["success"] === true) {
                window.location.href = "/chatapp";
            } else {
                $("#error-message").css({"display": "block"}).html(result["message"])
                hightlightError()
            }
        }

    // Register mode
    } else {
        let form = new FormData(this)
        let res = await fetch("/login?method=register" , {
            method: "post",
            body: form
        })

        if (res.ok) {
            let result = await res.json()
            if (result["success"] === true) {
                window.location.href = "/login";
            } else {
                $("#error-message").css({"display": "block"}).html(result["message"])
                hightlightError()
            }
        }
    }
    $(this).find("button[type=submit]").removeAttr("disabled")
})
<?php
require_once("DB.php");
require_once('Model.php');

Class ChatappModel extends Model {
    private $con;

    public function __construct() {
        parent::__construct();
        $this->con = DB::connect();
    }

    public function getUsersByUsername($userID, $username) {
        $username = '%'.$username.'%';

        if ($username === '%%') {
            return ["success" => true, "data" => []];
        }

        $sql = "SELECT * FROM `accounts` WHERE `userID` != ? AND `username` LIKE ?";
        $stmt = $this->con->prepare($sql);
        $stmt->bind_param("ss", $userID, $username);
        if ($stmt->execute()) {
            $users = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
            return ["success" => true, "data" => $users];
        } else {
            return ["success" => false, "message" => "Error executing query"];
        }
    }

    public function getNewestMessage($userID, $anoUserID) {
        $sql = "SELECT * FROM `messages` WHERE (`sending_user_ID` = ? AND `recieving_user_ID` = ?) OR (`sending_user_ID` = ? AND `recieving_user_ID` = ?) ORDER BY `msgID` DESC LIMIT 1";
        $stmt = $this->con->prepare($sql);
        $stmt->bind_param("ssss", $userID, $anoUserID, $anoUserID, $userID);
        if ($stmt->execute()) {
            $row = $stmt->get_result();
            if ($row->num_rows > 0) {
                $result = $row->fetch_assoc();
                return ["success" => true, "data" => $result];
            } else {
                return ["success" => false, "message" => "No message found"];
            }
        } else {
            return ["success" => false, "message" => "Error executing query"];
        }
    }
}